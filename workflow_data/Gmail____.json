{"name": "Gmail邮件总结", "id": "metdNAlrMqqBCKfn", "nodes": [{"id": "70c017f7-dbeb-4be6-a220-f2faa3dfba08", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [100, 200], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24}]}, "triggerAtHour": 9, "triggerAtMinute": 0}, "name": "每日触发器"}, {"id": "b90388ef-3a70-4fba-b2c7-62526bc04bf4", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [300, 200], "parameters": {"operation": "getAll", "returnAll": false, "limit": 50, "filters": {"receivedAfter": "={{ $now.minus({days: 1}).toISO() }}", "q": "is:unread"}, "format": "resolved", "download": false}, "name": "获取今日邮件"}, {"id": "7869b966-971e-44e1-b614-c8713e0fc958", "type": "n8n-nodes-base.aiTransform", "typeVersion": 1, "position": [500, 200], "parameters": {"instructions": "请分析这些Gmail邮件数据，为每封邮件提供以下信息的总结：\n\n1. 发件人姓名和邮箱地址\n2. 邮件主题\n3. 接收时间\n4. 邮件内容的简要摘要（不超过100字）\n5. 重要程度评级（高/中/低）\n\n请用中文输出，格式清晰易读。如果没有邮件，请返回'今日暂无新邮件'。"}, "name": "邮件内容总结"}, {"id": "47d465d2-12d3-428c-a0b6-0552a779bb4f", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [700, 200], "parameters": {"functionCode": "// 获取当前日期\nconst today = new Date().toLocaleDateString('zh-CN', {\n  year: 'numeric',\n  month: 'long', \n  day: 'numeric',\n  weekday: 'long'\n});\n\n// 获取AI总结的结果\nconst summary = $input.all()[0].json.output || '今日暂无新邮件';\n\n// 格式化最终报告\nconst report = {\n  date: today,\n  title: `📧 ${today} Gmail邮件总结报告`,\n  summary: summary,\n  timestamp: new Date().toISOString(),\n  emailCount: $('获取今日邮件').all().length || 0\n};\n\nreturn [{ json: report }];"}, "name": "格式化输出"}], "connections": {"每日触发器": {"main": [[{"node": "获取今日邮件", "type": "main", "index": 0}]]}, "获取今日邮件": {"main": [[{"node": "邮件内容总结", "type": "main", "index": 0}]]}, "邮件内容总结": {"main": [[{"node": "格式化输出", "type": "main", "index": 0}]]}}, "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "40f34be3-cfb8-4e7b-bd20-153814132585", "meta": {"instanceId": "c6d7ef5ef07edaa34acfc61bd66ee2c6f577e7008c1c805edcba838571f836d6"}, "tags": []}